<template>
  <div class="bg-bac h-full">
    <el-scrollbar height="100%">
      <div class="bg-w m-5 rounded-lg p-4">
        <el-descriptions>
          <template #title>
            <div class="flex cursor-pointer flex-wrap items-center" @click="router.back()">
              <el-icon size="20px"><Back /></el-icon>
              <div class="ml-2 break-words">{{ detailData.datasetNameCn }}</div>
            </div>
          </template>
          <el-descriptions-item label="数据集名称(中文)">{{ detailData.datasetNameCn }}</el-descriptions-item>
          <!-- <el-descriptions-item label="数据集名称(英文)">{{ detailData.datasetName }}</el-descriptions-item> -->
          <el-descriptions-item label="课题编码缩写">{{ detailData?.projectCode || '' }}</el-descriptions-item>
          <!-- <el-descriptions-item label="数据集说明">{{ detailData.description }}</el-descriptions-item> -->
          <el-descriptions-item label="疾病类型">
            {{ detailData.diseaseTypeAnnotation }}
          </el-descriptions-item>
          <el-descriptions-item label="更新日期">
            {{ detailData?.createDate }}
          </el-descriptions-item>
          <el-descriptions-item label="数据负责人">{{ detailData?.dataManager || '' }}</el-descriptions-item>
          <el-descriptions-item label="所属单位">{{ detailData?.affiliatedUnit || '' }}</el-descriptions-item>
          <el-descriptions-item label="所属项目">{{ detailData?.projectName || '' }}</el-descriptions-item>
          <el-descriptions-item label="所属项目负责人">{{ detailData?.projectLeader || '' }}</el-descriptions-item>
          <el-descriptions-item label="所属项目单位">{{
            detailData?.affiliatedProjectUnit || ''
          }}</el-descriptions-item>
          <el-descriptions-item label="上传人">{{ detailData?.uploader || '' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detailData?.contactPhone || '' }}</el-descriptions-item>
          <el-descriptions-item label="办公邮箱">{{ detailData?.officeEmail || '' }}</el-descriptions-item>
          <el-descriptions-item label="其他数据类型">{{ detailData?.otherDataTypes || '' }}</el-descriptions-item>
          <el-descriptions-item label="是否有生物样本">
            {{ detailData?.hasBiologicalSample }}
          </el-descriptions-item>
          <el-descriptions-item label="是否有影像数据">
            {{ detailData?.hasImagingData }}
          </el-descriptions-item>
          <el-descriptions-item label="是否有脑电数据">
            {{ detailData?.hasEEGData }}
          </el-descriptions-item>
          <el-descriptions-item label="生物样本类型">
            {{ detailData?.biologicalSampleType }}
          </el-descriptions-item>
          <el-descriptions-item label="数据模态类型">
            {{ detailData?.dataModalityType }}
          </el-descriptions-item>
          <el-descriptions-item label="数据信号类型">
            {{ detailData?.dataSignalType }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label="存放目录">{{ detailData?.parentTitle || '' }}</el-descriptions-item> -->
        </el-descriptions>
      </div>

      <h3 class="m-5 text-lg font-bold">数据集数据字段列表</h3>
      <div v-loading="loading" class="bg-w m-5 mb-0 rounded-md p-4">
        <div class="mb-4 flex gap-2">
          <el-select v-model="sheet" style="width: 160px" placeholder="选择量表" @change="fetchData()">
            <el-option v-for="item in sheets" :key="item.id" :label="item.tableChineseName" :value="item.id!">
              <div>
                <span>{{ item.tableChineseName }}</span>
                <span v-if="item.visitPhaseChinese">（{{ item.visitPhaseChinese }}）</span>
              </div>
            </el-option>
          </el-select>

          <el-input
            v-model="searchVal"
            placeholder="请输入关键字搜索"
            style="width: 300px"
            clearable
            @clear="fetchData()"
            @keyup.enter="fetchData()"
          >
            <template #append>
              <el-button :icon="Search" @click="fetchData()" />
            </template>
          </el-input>
        </div>
        <el-table :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="id" label="字段ID" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="gotoFiled(row)">
                {{ row.id }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="量表英文名称">
            <template #default="{ row }">
              {{ row.tableName }}<span v-if="row.visitPhase">（{{ row.visitPhase }}）</span>
            </template>
          </el-table-column>
          <el-table-column label="量表中文名称">
            <template #default="{ row }">
              {{ row.tableChineseName }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="变量名称" />
          <el-table-column prop="chineseMeaning" label="变量中文含义" />
          <el-table-column label="类型">
            <template #default="{ row }">
              <span>{{ fieldTypeText(row.valueType) }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="baseInfo" label="基本信息" /> -->
          <!-- <el-table-column label="是否按性别区分">
            <template #default="{ row }">
              <span>{{ row.gendered ? '是' : '否' }}</span>
            </template>
          </el-table-column> -->
          <!-- <el-table-column prop="explanation" label="变量说明" />
          <el-table-column prop="valueExplanation" label="变量值说明" />
          <el-table-column prop="notes" label="变量备注" /> -->
          <!-- <el-table-column prop="id" label="更新时间" width="180">
            <template #default="{ row }">
              {{ row.rltTime.updateTime }}
            </template>
          </el-table-column> -->
          <el-table-column v-if="props.desensitization" label="是否脱敏" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.needScrubbing"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="updateNeedScrubbing(row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column v-else label="操作" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="gotoFiled(row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="mt-5 flex justify-center">
          <el-pagination
            background
            layout="total, prev, pager, next, jumper"
            :page-size="pagination.pageSize"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <h3 v-if="props.showOriginalSheet" class="m-5 text-lg font-bold">原始量表数据字段列表</h3>
      <div v-if="props.showOriginalSheet" v-loading="loading2" class="bg-w m-5 mb-0 rounded-md p-4">
        <div class="mb-4 flex gap-2">
          <el-select v-model="sheet2" style="width: 160px" placeholder="选择量表" @change="fetchData2()">
            <el-option v-for="item in sheets2" :key="item.id" :label="item.title" :value="item.id!">
              <div>
                <span>{{ item.title }}</span>
                <span v-if="item.visitPhaseChinese">（{{ item.visitPhaseChinese }}）</span>
              </div>
            </el-option>
          </el-select>

          <el-input
            v-model="searchVal2"
            placeholder="请输入关键字搜索"
            style="width: 300px"
            clearable
            @clear="fetchData2()"
            @keyup.enter="fetchData2()"
          >
            <template #append>
              <el-button :icon="Search" @click="fetchData2()" />
            </template>
          </el-input>
        </div>
        <el-table :data="tableData2" style="width: 100%" class="c-table-header">
          <el-table-column prop="id" label="ID" width="100" />
          <el-table-column prop="name" label="字段英文名称" />
          <el-table-column label="字段类型">
            <template #default="{ row }">
              <span>{{ fieldTypeText(row.dataType) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="chineseMeaning" label="字段中文名称" />
          <el-table-column prop="state" label="字段状态" />
          <el-table-column prop="sheetCode" label="工作表英文名称" />
          <el-table-column prop="length" label="长度" />
          <el-table-column prop="maximumValue" label="最大值" />
          <el-table-column prop="minimumValue" label="最小值" />
          <el-table-column prop="explanation" label="变量说明" show-overflow-tooltip />
          <el-table-column prop="valueExplanation" label="变量值说明" show-overflow-tooltip />
          <el-table-column prop="notes" label="变量备注" show-overflow-tooltip />
        </el-table>

        <div class="mt-5 flex justify-center">
          <el-pagination
            background
            layout="total, prev, pager, next, jumper"
            :page-size="pagination2.pageSize"
            :total="total2"
            @current-change="handleCurrentChange2"
          />
        </div>
      </div>
      <div class="h-5"></div>
    </el-scrollbar>
  </div>
</template>

<!-- 数据集数据字段详情 -->
<script setup lang="ts">
  import {
    findEntityById_36,
    findMedicalFieldsByFileInforIdAndDynamicConditions,
    findMedicalFieldsByTableId,
    findTablesByFileInforId,
    newOrUpdateMedicalFieldVO,
    findOriginalSheetByFileId,
    findOriginalColumnByOriginalSheetId,
  } from '@/api';
  import { useRouter } from 'vue-router';
  import { Search } from '@element-plus/icons-vue';
  import { fieldTypeText } from '@/utils/format';
  import { ElMessage } from 'element-plus';

  const router = useRouter();
  interface Props {
    id: string;
    desensitization?: boolean;
    showOriginalSheet?: boolean;
  }
  const props = defineProps<Props>();

  // 数据相关
  const detailData = ref<FileInfoVO>({} as any);
  const tableData = ref<MedicalFieldVO[]>([]);
  const loading = ref(false);
  const total = ref(0);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });

  // 搜索相关
  const searchVal = ref('');
  const valueType = ref('');
  const valueTypes = ref([
    { value: 'S', label: '字符串' },
    { value: 'N', label: '整数' },
    { value: 'F', label: '小数' },
    { value: 'D', label: '日期' },
    { value: 'DT', label: '日期时间' },
    { value: 'T', label: '文本' },
    { value: 'E', label: '枚举' },
  ]);
  const sheet = ref<string>('');
  const sheets = ref<CBDDefTableVO[]>([]);

  // 生命周期钩子
  onBeforeMount(() => {
    fetchDetail();
    fetchSheet().then(() => {
      if (sheets.value.length > 0) {
        sheet.value = String(sheets.value[0].id!);
        fetchData();
      } else {
        ElMessage.warning('暂无量表数据');
      }
    });
    if (props.showOriginalSheet) {
      fetchSheet2();
    }
  });

  // 数据获取相关函数
  async function fetchDetail() {
    try {
      loading.value = true;
      const { data } = await findEntityById_36(+props.id);
      detailData.value = data || ({} as any);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      let data: VOPageMedicalFieldMedicalFieldVO | undefined;
      if (!sheet.value) {
        ElMessage.warning('请选择量表');
        tableData.value = [];
        total.value = 0;
        loading.value = false;
        return;
      }
      const res = await findMedicalFieldsByTableId({
        // cbdTableId: [undefined, null, NaN, ''].includes(+sheet.value) ? 0 : +sheet.value,
        cbdTableId: +sheet.value,
        pageNum,
        pageSize: pagination.pageSize,
        searchInput: searchVal.value,
      });
      data = res.data;
      pagination.page = pageNum;
      total.value = data?.totalElement || 0;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  async function fetchSheet() {
    const { data } = await findTablesByFileInforId(+props.id, 1, 99);
    sheets.value = data?.content || [];
  }

  const loading2 = ref(false);
  const sheet2 = ref<string>('');
  const sheets2 = ref<OriginalSheetVO[]>([]);
  async function fetchSheet2() {
    try {
      loading2.value = true;
      const { data } = await findOriginalSheetByFileId(+props.id, 1, 99, { searchInput: '' } as any);
      sheets2.value = data?.content || [];
      // 默认选中第一个选项
      if (sheets2.value.length > 0) {
        sheet2.value = String(sheets2.value[0].id!);
        loading2.value = false;
        fetchData2();
      }
    } catch (error) {
      console.log(error);
      loading2.value = false;
    }
  }

  const searchVal2 = ref('');
  const tableData2 = ref<OriginalColumnVO[]>([]);
  const total2 = ref(0);
  const pagination2 = reactive({
    page: 1,
    pageSize: 10,
  });
  async function fetchData2(pageNum = 1) {
    try {
      loading2.value = true;
      const res = await findOriginalColumnByOriginalSheetId(
        // [undefined, null, NaN, ''].includes(+sheet2.value) ? 0 : +sheet2.value,
        +sheet2.value,
        pageNum,
        pagination2.pageSize,
        {
          searchInput: searchVal2.value,
        } as any
      );
      const data = res.data;
      pagination2.page = pageNum;
      total2.value = data?.totalElement || 0;
      tableData2.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading2.value = false;
    }
  }

  const handleCurrentChange2 = (e) => {
    pagination2.page = e;
    fetchData2(e);
  };

  // 事件处理函数
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };

  const updateNeedScrubbing = async (row: MedicalFieldVO) => {
    try {
      loading.value = true;
      await newOrUpdateMedicalFieldVO(row);
      await fetchData(pagination.page);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const gotoFiled = (row: any) => {
    router.push({
      name: 'DataSetFieldDetail',
      query: { fieldId: row.id },
    });
  };
</script>
