<template>
  <el-dialog v-model="visible" title="选择数据集" width="80%" :before-close="handleClose" destroy-on-close>
    <div class="data-select-container">
      <!-- 搜索栏 -->
      <div class="search-bar mb-4">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入数据集名称搜索"
          clearable
          @input="onSearch"
          style="width: 300px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 数据集列表 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        class="c-table c-table-header"
        @selection-change="handleSelectionChange"
        :row-key="(row) => row.id"
        ref="tableRef"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="datasetNameCn" label="数据集中文名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="datasetName" label="数据集英文名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="projectCode" label="项目编号" width="120" />
        <el-table-column prop="diseaseType" label="病种" width="100" />
        <el-table-column prop="caseCount" label="病例数量" width="100" />
        <el-table-column prop="createDate" label="创建日期" width="120" />
        <el-table-column prop="dataManager" label="数据负责人" width="120" />
        <el-table-column prop="affiliatedUnit" label="所属单位" width="150" show-overflow-tooltip />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container mt-4 flex justify-center">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="saveLoading">
          确定选择 ({{ selectedDatasets.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { findFileInforByAnnotationId, addInfor } from '@/api';
  import { ElMessage, ElTable } from 'element-plus';
  import { Search } from '@element-plus/icons-vue';
  import { debounce } from 'lodash-es';

  interface Props {
    visible: boolean;
    applicationId: string | undefined;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  // 响应式数据
  const loading = ref(false);
  const saveLoading = ref(false);
  const searchKeyword = ref('');
  const tableData = ref<FileInfoVO[]>([]);
  const selectedDatasets = ref<FileInfoVO[]>([]);
  const tableRef = ref<InstanceType<typeof ElTable>>();

  // 分页数据
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 获取数据集列表
  const fetchDatasets = async (pageNum = 1) => {
    try {
      loading.value = true;
      const params: AnnotationIDListDTO = {
        annotationIDList: [],
        pageNum,
        pageSize: pagination.pageSize,
        otherFilter: searchKeyword.value,
      };

      const { data } = await findFileInforByAnnotationId(params);
      tableData.value = data?.content || [];
      total.value = data?.totalElement || 0;
    } catch (error) {
      console.error('获取数据集失败:', error);
      ElMessage.error('获取数据集失败');
    } finally {
      loading.value = false;
    }
  };

  // 处理选择变化
  const handleSelectionChange = (selection: FileInfoVO[]) => {
    selectedDatasets.value = selection;
  };

  // 搜索防抖
  const onSearch = debounce(() => {
    fetchDatasets(1);
  }, 300);

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    fetchDatasets(1);
  };

  const handleCurrentChange = (page: number) => {
    fetchDatasets(page);
  };

  // 确认选择
  const handleConfirm = async () => {
    if (selectedDatasets.value.length === 0) {
      ElMessage.warning('请至少选择一个数据集');
      return;
    }

    if (!props.applicationId) {
      ElMessage.error('项目申请ID不能为空');
      return;
    }

    try {
      saveLoading.value = true;
      const datasetIds = selectedDatasets.value.map(dataset => dataset.id!);
      
      await addInfor(datasetIds, { applicationId: +props.applicationId });
      
      ElMessage.success(`成功添加 ${selectedDatasets.value.length} 个数据集到项目申请`);
      emit('success');
      handleClose();
    } catch (error) {
      console.error('添加数据集失败:', error);
      ElMessage.error('添加数据集失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 关闭对话框
  const handleClose = () => {
    selectedDatasets.value = [];
    searchKeyword.value = '';
    pagination.page = 1;
    tableRef.value?.clearSelection();
    visible.value = false;
  };

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (newVisible) => {
      if (newVisible) {
        fetchDatasets(1);
      }
    }
  );
</script>

<style lang="scss" scoped>
  .data-select-container {
    min-height: 400px;
  }

  .search-bar {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .pagination-container {
    margin-top: 16px;
  }

  .c-table {
    --el-border-color-lighter: #ebeef5;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
</style>
