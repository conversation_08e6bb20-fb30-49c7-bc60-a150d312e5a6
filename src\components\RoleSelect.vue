<template>
  <el-select v-model="modelValue" placeholder="请选择角色" clearable filterable multiple>
    <el-option v-for="item in filteredRoles" :key="item.id" :label="item.roleName" :value="item.id!" />
  </el-select>
</template>

<script setup lang="ts">
  import { findAll_2_02 } from '@/api';

  const modelValue = defineModel<string[]>({ required: true });

  const emit = defineEmits(['update:modelValue']);

  const roles = ref<RoleVO[]>([]);

  // 过滤后的角色列表，屏蔽项目成员和项目负责人角色
  const filteredRoles = computed(() => {
    return roles.value.filter((role) => {
      // 屏蔽项目负责人角色（角色代码为 ApplicationAdministrator）
      if (role.roleCode === 'ApplicationAdministrator') {
        return false;
      }

      // 屏蔽项目成员角色（角色名称包含"项目成员"、"成员"或等于"项目成员"）
      const roleName = role.roleName?.toLowerCase() || '';
      if (roleName.includes('项目成员') || roleName.includes('成员') || roleName === '项目成员') {
        return false;
      }

      return true;
    });
  });

  // 获取角色列表
  async function fetchRoles() {
    try {
      const { data } = await findAll_2_02(1, 99);
      const anyData = data as any;
      roles.value = anyData?.content || [];
    } catch (error) {
      console.error(error);
    }
  }

  fetchRoles();
</script>
