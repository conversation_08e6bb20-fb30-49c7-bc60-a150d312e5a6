<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">脱敏管理</h2>

    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="flex justify-between px-10">
        <el-input
          v-model="search"
          placeholder="请输入关键字"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
          <!-- <el-table-column prop="datasetName" min-width="100px" label="数据集名称(英文)" show-overflow-tooltip /> -->
          <el-table-column prop="projectCode" label="课题编码缩写" />
          <!-- <el-table-column prop="description" label="数据集说明" show-overflow-tooltip /> -->
          <el-table-column prop="diseaseType" label="初始疾病类型" />
          <el-table-column prop="createDate" label="更新日期" width="170px" />
          <el-table-column prop="dataManager" label="数据负责人" />
          <el-table-column prop="affiliatedUnit" label="所属单位" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button link type="primary" @click="onViewDetail(row)">查看</el-button>
              <!-- <el-button link type="primary" @click="onDownload(row)">下载</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { findFileInforByAnnotationId } from '@/api';
  import { download } from '@/utils/request';
  import { Search } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const loading = ref(false);
  const search = ref('');
  const onSearch = () => {
    fetchData();
  };

  //表格
  const tableData = ref<FileInfoVO[]>([]);
  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      let params: AnnotationIDListDTO = {
        annotationIDList: [],
        pageNum,
        pageSize: pagination.pageSize,
        otherFilter: search.value,
      };
      const { data } = await findFileInforByAnnotationId(params);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };

  //查看详情
  const onViewDetail = (row) => {
    router.push({ name: 'DesensitizationField', params: { id: row.id } });
  };

  //下载
  async function onDownload(row) {
    try {
      loading.value = true;
      await download(`/medicalData/downloadColumn`, {
        method: 'post',
        data: [row.id],
      });
      ElMessage({ type: 'success', message: '下载成功' });
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  onBeforeMount(() => {
    fetchData();
  });
</script>
