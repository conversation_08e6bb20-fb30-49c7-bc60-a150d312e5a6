<template>
  <el-dialog v-model="modelValue" title="字典值管理" width="90%" destroy-on-close>
    <div v-loading="loading">
      <div class="flex gap-4">
        <el-button type="primary" @click="onAdd">新增</el-button>
        <!-- <el-input
          v-model="search"
          placeholder="请输入编码搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->
      </div>

      <div class="mt-3 w-full">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="title" label="字典值" />
          <el-table-column prop="code" min-width="100px" label="编码" />
          <el-table-column prop="isDefault" label="是否是默认值">
            <template #default="{ row }">
              {{ row.isDefault ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag v-if="row.state" :type="row.state === '启用' ? 'success' : 'danger'">{{ row.state }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sortOrder" label="排序" />
          <el-table-column fixed="right" label="操作" width="80px">
            <template #default="{ row }">
              <el-tooltip content="编辑" effect="dark">
                <el-button link type="primary" icon="edit" @click="onEdit(row)" />
              </el-tooltip>
              <el-popconfirm title="确定删除？" @confirm="onDel(row)">
                <template #reference>
                  <span class="ml-3">
                    <el-tooltip content="删除" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </span>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog v-model="showAdd" :title="formTitle" width="600px" @close="onAddClose">
      <el-form ref="formRef" :model="addForm" :rules="rules" label-width="140px">
        <el-form-item label="字典值" prop="title">
          <el-input v-model="addForm.title" placeholder="请输入字典值" />
        </el-form-item>
        <el-form-item label="字典编码" prop="code">
          <el-input v-model="addForm.code" :disabled="addForm.id ? true : false" placeholder="请输入字典编码" />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-switch
            v-model="addForm.state"
            active-value="启用"
            inactive-value="停用"
            active-text="启用"
            inactive-text="停用"
            inline-prompt
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="addForm.sortOrder" :min="1" :step="1" :controls="true"> </el-input-number>
        </el-form-item>
        <el-form-item label="是否默认值" prop="isDefault">
          <el-radio-group v-model="addForm.isDefault">
            <el-radio
              v-for="item in [
                { label: '是', value: true },
                { label: '否', value: false },
              ]"
              :key="item.label"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onAddClose">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </span>
    </template> -->
  </el-dialog>
</template>

<script setup lang="ts">
  import { deleteEntityById_40, findByState, newOrUpdateEntity_12 } from '@/api';
  import { add } from '@jsplumb/browser-ui';
  import { ElMessage, FormInstance } from 'element-plus';
  import { cloneDeep } from 'lodash-es';

  interface Props {
    id: string | number;
  }
  const props = defineProps<Props>();
  const modelValue = defineModel<boolean>({ required: true });

  const loading = ref(false);

  //---------新增字典-----------
  const formRef = ref<FormInstance>();
  const addForm = reactive<DictionaryValueDTO>({
    id: 0,
    dictionaryId: 0,
    code: '',
    title: '',
    sortOrder: 100,
    state: '启用',
    value: '',
    isDefault: false,
  });
  const rules = ref({
    id: [{ required: true, message: '不能为空' }],
    dictionaryId: [{ required: true, message: '不能为空' }],
    code: [{ required: true, message: '不能为空' }],
    title: [{ required: true, message: '不能为空' }],
    sortOrder: [{ required: true, message: '不能为空' }],
    state: [{ required: true, message: '不能为空' }],
    value: [{ required: true, message: '不能为空' }],
    isDefault: [{ required: true, message: '不能为空' }],
  });
  const showAdd = ref(false);
  const addLoading = ref(false);
  const formTitle = computed(() => (addForm.id ? '编辑字典值' : '新增字典值'));

  const onAdd = () => {
    // 重置表单数据到初始状态
    Object.assign(addForm, {
      id: 0,
      dictionaryId: 0,
      code: '',
      title: '',
      sortOrder: 100,
      state: '启用',
      value: '',
      isDefault: false,
    });
    formRef.value?.resetFields();
    showAdd.value = true;
  };
  const onEdit = (row: FileInfoVO) => {
    formRef.value?.resetFields();
    showAdd.value = true;
    nextTick(() => {
      for (const key in addForm) {
        if (Object.prototype.hasOwnProperty.call(addForm, key)) {
          addForm[key] = row[key];
        }
      }
    });
  };
  const onAddClose = () => {
    showAdd.value = false;
  };
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          const copyForm = cloneDeep(addForm);
          if (!copyForm.id) {
            copyForm.dictionaryId = +props.id;
            copyForm.value = copyForm.title;
          }
          await newOrUpdateEntity_12(copyForm);
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  //----------字典列表----------
  const tableData = ref<DictionaryValueVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findByState(+props.id, pagination.page, pagination.pageSize);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData();
  };

  //删除
  async function onDel(row: DictionaryValueVO) {
    try {
      loading.value = true;
      await deleteEntityById_40(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  watch(modelValue, () => {
    if (modelValue.value) {
      pagination.page = 1;
      fetchData();
    }
  });

  const emit = defineEmits<{ success: [] }>();
  const onCancel = () => {
    modelValue.value = false;
  };
  const onSave = () => {
    onCancel();
    emit('success');
  };
</script>
