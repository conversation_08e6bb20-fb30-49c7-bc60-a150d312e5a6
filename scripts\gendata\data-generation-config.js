// 数据生成配置文件

// 环境配置
export const ENVIRONMENTS = {
  production: {
    baseUrl: 'http://*************:31010',
    authorization:
      'Bearer 5EWyUg-rmoLoFbhpMrmUln2IOXcIfv7B1dP0dYLx6j5xMwiW9_MzKryDwpAIoQvFGAVzZ74ojE642NwQ5XEiCDwwMTu4B6a77MWt1kDI8djITCmZnWPld3L2OaZuiPivd',
    referer: 'http://*************:31010/background/dictionary',
  },
  staging: {
    baseUrl: 'http://staging-server:port',
    authorization: 'Bearer staging-token',
    referer: 'http://staging-server:port/background/dictionary',
  },
  development: {
    baseUrl: 'http://localhost:8080',
    authorization: 'Bearer dev-token',
    referer: 'http://localhost:8080/background/dictionary',
  },
};

// 字典类型配置
export const DICTIONARY_TYPES = {
  DISEASE: {
    prefix: 'DISEASE',
    dictionaryId: 7,
    description: '疾病类型字典',
  },
  ORGANIZATION: {
    prefix: 'ORGANIZATION',
    dictionaryId: 6,
    description: '机构字典',
  },
};

// 字典值数据模板
export const DICTIONARY_DATA_TEMPLATES = {
  // 疾病类型
  diseases: [
    { title: '药物成瘾', value: '药物成瘾', type: 'DISEASE', sortOrder: 100 },
    { title: '心理健康', value: '心理健康', type: 'DISEASE', sortOrder: 101 },
    { title: '焦虑障碍', value: '焦虑障碍', type: 'DISEASE', sortOrder: 102 },
    { title: '睡眠障碍', value: '睡眠障碍', type: 'DISEASE', sortOrder: 103 },
  ],
};

// API端点配置
export const API_ENDPOINTS = {
  // 字典相关
  dictionary: {
    createOrUpdate: '/resources/DictionaryValue/newOrUpdateEntity',
    findByDictionaryId: '/resources/DictionaryValue/findDictionaryValue', // {dictionaryId}/{pageNum}/{pageSize}
  },

  // 文件信息相关
  fileInfo: {
    findByAnnotationId: '/resources/FileInfor/findFileInforByAnnotationId',
  },
};

// 数据生成规则配置
export const GENERATION_RULES = {
  // 编码生成规则
  codeGeneration: {
    dateFormat: 'YYYYMMDD',
    timeFormat: 'HHmmss',
    separator: '',
    includeRandom: false,
    randomLength: 4,
  },

  // 请求配置
  request: {
    timeout: 30000,
    retryCount: 3,
    retryDelay: 1000,
    batchSize: 10,
    requestInterval: 500,
  },

  // 日志配置
  logging: {
    level: 'info', // debug, info, warn, error
    saveToFile: true,
    logDir: './logs',
    maxFileSize: '10MB',
    maxFiles: 5,
  },
};

// 默认请求头配置
export const DEFAULT_HEADERS = {
  'User-Agent':
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
  Accept: 'application/json, text/plain, */*',
  'Content-Type': 'application/json',
};

// 数据验证规则
export const VALIDATION_RULES = {
  dictionary: {
    title: {
      required: true,
      maxLength: 100,
      minLength: 1,
    },
    value: {
      required: true,
      maxLength: 200,
      minLength: 1,
    },
    code: {
      required: true,
      pattern: /^[A-Z0-9_]+$/,
      maxLength: 50,
    },
    sortOrder: {
      required: true,
      type: 'number',
      min: 0,
      max: 9999,
    },
  },
};

// 错误处理配置
export const ERROR_HANDLING = {
  // HTTP状态码处理
  httpStatusHandling: {
    400: '请求参数错误',
    401: '认证失败，请检查Authorization token',
    403: '权限不足',
    404: '接口不存在',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
  },

  // 业务错误码处理
  businessErrorHandling: {
    DUPLICATE_CODE: '字典编码重复',
    INVALID_DICTIONARY_ID: '无效的字典ID',
    PERMISSION_DENIED: '权限不足',
  },
};

// 导出默认配置
export default {
  ENVIRONMENTS,
  DICTIONARY_TYPES,
  DICTIONARY_DATA_TEMPLATES,
  API_ENDPOINTS,
  GENERATION_RULES,
  DEFAULT_HEADERS,
  VALIDATION_RULES,
  ERROR_HANDLING,
};
