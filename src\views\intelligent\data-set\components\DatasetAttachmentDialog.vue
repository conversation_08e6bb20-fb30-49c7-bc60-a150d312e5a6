<template>
  <el-dialog
    v-model="modelValue"
    title="数据集附件管理"
    width="80%"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onClose"
  >
    <div v-loading="loading" class="attachment-container">
      <!-- 操作栏 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex gap-4">
          <el-button type="primary" :icon="Plus" @click="onAdd">新增附件</el-button>
          <el-button v-if="selectedAttachments.length > 0" type="danger" :icon="Delete" @click="onBatchDelete">
            批量删除
          </el-button>
        </div>
        <div class="text-sm text-gray-500">
          数据集：{{ currentDataset?.datasetNameCn || currentDataset?.datasetName }}
        </div>
      </div>

      <!-- 附件列表 -->
      <el-table
        ref="tableRef"
        :data="attachmentList"
        style="width: 100%"
        class="c-table-header"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="nameCN" label="附件名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="name" label="文件名" min-width="150" show-overflow-tooltip />
        <el-table-column prop="type" label="附件类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="caseCount" label="病例数量" width="100" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="{ row }">
            <div class="flex gap-2">
              <el-button link type="primary" @click="onEdit(row)">编辑</el-button>
              <el-button link type="primary" @click="onReplace(row)">替换</el-button>
              <el-popconfirm title="确定删除该附件？" @confirm="onDelete(row)">
                <template #reference>
                  <el-button link type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container mt-4">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          :current-page="pagination.page"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑附件弹窗 -->
    <el-dialog v-model="showForm" :title="formTitle" width="600px" destroy-on-close append-to-body @close="onFormClose">
      <el-form ref="formRef" :model="attachmentForm" :rules="formRules" label-width="120px">
        <el-form-item label="附件名称" prop="nameCN">
          <el-input v-model="attachmentForm.nameCN" placeholder="请输入附件中文名称" />
        </el-form-item>

        <el-form-item label="附件类型" prop="type">
          <el-select v-model="attachmentForm.type" placeholder="请选择附件类型" style="width: 100%">
            <el-option label="多组学数据文件" value="BIOLOGICAL" />
          </el-select>
        </el-form-item>

        <el-form-item label="病例数量" prop="caseCount">
          <el-input v-model="attachmentForm.caseCount" placeholder="请输入病例数量" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="attachmentForm.description" type="textarea" :rows="3" placeholder="请输入附件描述" />
        </el-form-item>

        <el-form-item label="附件文件" prop="file" v-if="!isEdit || isReplace">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">支持各种格式的多组学数据文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onFormClose">取消</el-button>
          <el-button type="primary" :loading="formLoading" @click="onFormConfirm">
            {{ isReplace ? '替换' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from 'vue';
  import { ElMessage, ElMessageBox, FormInstance, UploadFiles, UploadFile } from 'element-plus';
  import { Plus, Delete, UploadFilled } from '@element-plus/icons-vue';
  import { findDatasetAttachmentByCriteria, newOrUpdateEntity_9_02, deleteDatasetAttachmentById } from '@/api';
  import { upload } from '@/utils/request';

  // 组件属性
  interface Props {
    modelValue: boolean;
    currentDataset: FileInfoVO | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    'update:modelValue': [value: boolean];
  }>();

  // 计算属性用于双向绑定
  const modelValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 响应式数据
  const loading = ref(false);
  const attachmentList = ref<DatasetAttachmentVO[]>([]);
  const selectedAttachments = ref<DatasetAttachmentVO[]>([]);
  const total = ref(0);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });

  // 表单相关
  const showForm = ref(false);
  const formLoading = ref(false);
  const isEdit = ref(false);
  const isReplace = ref(false);
  const formRef = ref<FormInstance>();
  const uploadRef = ref();
  const fileList = ref<UploadFiles>([]);
  const tableRef = ref();

  const attachmentForm = reactive<DatasetAttachmentDTO>({
    id: 0,
    fileInforId: 0,
    name: '',
    nameCN: '',
    type: 'BIOLOGICAL',
    description: '',
    caseCount: '',
  });

  // 表单验证规则
  const formRules = {
    nameCN: [{ required: true, message: '请输入附件名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择附件类型', trigger: 'change' }],
    file: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!isEdit.value || isReplace.value) {
            if (fileList.value.length === 0) {
              callback(new Error('请选择要上传的文件'));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: 'change',
      },
    ],
  };

  // 计算属性
  const formTitle = computed(() => {
    if (isReplace.value) return '替换附件';
    return isEdit.value ? '编辑附件' : '新增附件';
  });

  // 获取附件类型标签样式
  const getTypeTagType = (type: string) => {
    switch (type) {
      case 'BIOLOGICAL':
        return 'success';
      case 'EEG':
        return 'warning';
      case 'IMAGING':
        return 'info';
      default:
        return '';
    }
  };

  // 获取附件类型标签文本
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'BIOLOGICAL':
        return '多组学数据文件';
      case 'EEG':
        return '心电数据';
      case 'IMAGING':
        return '影像数据';
      default:
        return type;
    }
  };

  // 获取附件列表
  const fetchAttachments = async (pageNum = 1) => {
    if (!props.currentDataset?.id) return;

    try {
      loading.value = true;
      const { data } = await findDatasetAttachmentByCriteria({
        fileInforId: props.currentDataset.id,
        pageNum,
        pageSize: pagination.pageSize,
      });

      attachmentList.value = data?.content || [];
      total.value = data?.totalElement || 0;
      pagination.page = pageNum;
    } catch (error) {
      console.error('获取附件列表失败:', error);
      ElMessage.error('获取附件列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 处理选择变化
  const handleSelectionChange = (selection: DatasetAttachmentVO[]) => {
    selectedAttachments.value = selection;
  };

  // 分页变化
  const handleCurrentChange = (page: number) => {
    fetchAttachments(page);
  };

  // 文件上传处理
  const handleFileChange = (file: UploadFile) => {
    fileList.value = [file];
  };

  const handleFileRemove = () => {
    fileList.value = [];
  };

  // 新增附件
  const onAdd = () => {
    resetForm();
    isEdit.value = false;
    isReplace.value = false;
    showForm.value = true;
  };

  // 编辑附件
  const onEdit = (row: DatasetAttachmentVO) => {
    resetForm();
    isEdit.value = true;
    isReplace.value = false;

    Object.assign(attachmentForm, {
      id: row.id,
      fileInforId: row.fileInforId,
      name: row.name,
      nameCN: row.nameCN,
      type: row.type,
      description: row.description,
      caseCount: row.caseCount,
    });

    showForm.value = true;
  };

  // 替换附件
  const onReplace = (row: DatasetAttachmentVO) => {
    resetForm();
    isEdit.value = true;
    isReplace.value = true;

    Object.assign(attachmentForm, {
      id: row.id,
      fileInforId: row.fileInforId,
      name: row.name,
      nameCN: row.nameCN,
      type: row.type,
      description: row.description,
      caseCount: row.caseCount,
    });

    showForm.value = true;
  };

  // 删除单个附件
  const onDelete = async (row: DatasetAttachmentVO) => {
    try {
      await deleteDatasetAttachmentById(row.id!);
      ElMessage.success('删除成功');
      fetchAttachments(pagination.page);
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  };

  // 批量删除
  const onBatchDelete = async () => {
    if (selectedAttachments.value.length === 0) {
      ElMessage.warning('请选择要删除的附件');
      return;
    }

    try {
      await ElMessageBox.confirm('确定删除选中的附件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      for (const attachment of selectedAttachments.value) {
        await deleteDatasetAttachmentById(attachment.id!);
      }

      ElMessage.success('批量删除成功');
      selectedAttachments.value = [];
      fetchAttachments(pagination.page);
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        ElMessage.error('批量删除失败');
      }
    }
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(attachmentForm, {
      id: 0,
      fileInforId: props.currentDataset?.id || 0,
      name: '',
      nameCN: '',
      type: 'BIOLOGICAL',
      description: '',
      caseCount: '',
    });
    fileList.value = [];
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  // 表单确认
  const onFormConfirm = async () => {
    if (!formRef.value) return;

    const valid = await formRef.value.validate();
    if (!valid) return;

    try {
      formLoading.value = true;

      if (!isEdit.value || isReplace.value) {
        // 新增或替换需要上传文件
        if (fileList.value.length === 0) {
          ElMessage.error('请选择要上传的文件');
          return;
        }

        const formData = new FormData();
        formData.append('file', fileList.value[0].raw!);

        // 设置文件名
        attachmentForm.name = fileList.value[0].name;

        const attachmentBlob = new Blob([JSON.stringify(attachmentForm)], { type: 'application/json' });
        formData.append('datasetAttachmentFile', attachmentBlob, 'datasetAttachmentFile.json');

        await upload(`/datasetAttachment/uploadDatasetAttachment?fileId=${props.currentDataset?.id}`, {
          method: 'post',
          data: formData,
        });
      } else {
        // 仅编辑信息
        await newOrUpdateEntity_9_02(attachmentForm);
      }

      ElMessage.success(isReplace.value ? '替换成功' : isEdit.value ? '编辑成功' : '新增成功');
      onFormClose();
      fetchAttachments(pagination.page);
    } catch (error) {
      console.error('操作失败:', error);
      ElMessage.error('操作失败');
    } finally {
      formLoading.value = false;
    }
  };

  // 关闭表单弹窗
  const onFormClose = () => {
    showForm.value = false;
    resetForm();
  };

  // 关闭主弹窗
  const onClose = () => {
    modelValue.value = false;
  };

  // 监听弹窗显示状态
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue && props.currentDataset?.id) {
        fetchAttachments(1);
      }
    }
  );
</script>

<style lang="scss" scoped>
  .attachment-container {
    min-height: 400px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
  }
</style>
