/*
 * @OriginalName: 数据集附属文件管理模块
 * @Description: 数据集附属的生化、心电、图像等文件信息的管理
 */
import { request } from '@/utils/request';

/**
 * 上传数据集附属文件
 * @description 根据医学数据集文件ID，上传该数据集的附属文件，包括生化、心电、影像等类型文件。
 */
export function uploadDatasetAttachment(params?: { fileId: number }) {
  return request<R>(`/datasetAttachment/uploadDatasetAttachment`, {
    method: 'post',
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_9_02(data: DatasetAttachmentDTO) {
  return request<RDatasetAttachmentVO>(`/datasetAttachment/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_9_02(data: Array<number>) {
  return request<RListDatasetAttachmentVO>(`/datasetAttachment/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_9_02(data: Array<number>) {
  return request<R>(`/datasetAttachment/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_38_02(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListDatasetAttachmentVO>(`/datasetAttachment/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_39_02(id: number, params?: { id: number }) {
  return request<RDatasetAttachmentVO>(`/datasetAttachment/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数附属文件
 * @description 按数据集的id和附属文件名称条件，获取满足相应条件的附属文件信息。
 */
export function findDatasetAttachmentByCriteria(params?: {
  fileInforId: number;
  keyWord?: string;
  pageNum?: number;
  pageSize?: number;
}) {
  return request<RVOPageDatasetAttachmentDatasetAttachmentVO>(`/datasetAttachment/findDatasetAttachment`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_9_02(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/datasetAttachment/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_38_02(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/datasetAttachment/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteDatasetAttachmentById(id: number, params?: { id: number }) {
  return request<R>(`/datasetAttachment/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
