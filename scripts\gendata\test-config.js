import { 
  ENVIRONMENTS, 
  DICTIONARY_TYPES, 
  DICTIONARY_DATA_TEMPLATES, 
  API_ENDPOINTS,
  GENERATION_RULES,
  VALIDATION_RULES 
} from './data-generation-config.js';

// 测试配置完整性
function testConfiguration() {
  console.log('🧪 开始测试配置文件...\n');
  
  let hasErrors = false;
  
  // 测试环境配置
  console.log('📋 测试环境配置:');
  Object.entries(ENVIRONMENTS).forEach(([env, config]) => {
    console.log(`  ${env}:`);
    if (!config.baseUrl) {
      console.error(`    ❌ 缺少 baseUrl`);
      hasErrors = true;
    } else {
      console.log(`    ✅ baseUrl: ${config.baseUrl}`);
    }
    
    if (!config.authorization) {
      console.error(`    ❌ 缺少 authorization`);
      hasErrors = true;
    } else {
      console.log(`    ✅ authorization: ${config.authorization.substring(0, 20)}...`);
    }
  });
  
  // 测试字典类型配置
  console.log('\n📚 测试字典类型配置:');
  Object.entries(DICTIONARY_TYPES).forEach(([type, config]) => {
    console.log(`  ${type}:`);
    if (!config.prefix) {
      console.error(`    ❌ 缺少 prefix`);
      hasErrors = true;
    } else {
      console.log(`    ✅ prefix: ${config.prefix}`);
    }
    
    if (!config.dictionaryId) {
      console.error(`    ❌ 缺少 dictionaryId`);
      hasErrors = true;
    } else {
      console.log(`    ✅ dictionaryId: ${config.dictionaryId}`);
    }
  });
  
  // 测试数据模板
  console.log('\n📄 测试数据模板:');
  Object.entries(DICTIONARY_DATA_TEMPLATES).forEach(([templateType, data]) => {
    console.log(`  ${templateType}: ${data.length} 条数据`);
    
    data.forEach((item, index) => {
      if (!item.title || !item.value || !item.type) {
        console.error(`    ❌ 第${index + 1}条数据缺少必要字段`);
        hasErrors = true;
      }
      
      if (!DICTIONARY_TYPES[item.type]) {
        console.error(`    ❌ 第${index + 1}条数据的类型 ${item.type} 未在 DICTIONARY_TYPES 中定义`);
        hasErrors = true;
      }
    });
    
    if (!hasErrors) {
      console.log(`    ✅ 数据格式正确`);
    }
  });
  
  // 测试API端点配置
  console.log('\n🔗 测试API端点配置:');
  function checkEndpoints(endpoints, prefix = '') {
    Object.entries(endpoints).forEach(([key, value]) => {
      if (typeof value === 'string') {
        if (!value.startsWith('/')) {
          console.error(`    ❌ ${prefix}${key}: 端点应以 / 开头`);
          hasErrors = true;
        } else {
          console.log(`    ✅ ${prefix}${key}: ${value}`);
        }
      } else if (typeof value === 'object') {
        checkEndpoints(value, `${prefix}${key}.`);
      }
    });
  }
  
  checkEndpoints(API_ENDPOINTS);
  
  // 测试生成规则
  console.log('\n⚙️ 测试生成规则:');
  const rules = GENERATION_RULES;
  
  if (rules.request.timeout < 1000) {
    console.error(`    ❌ 请求超时时间过短: ${rules.request.timeout}ms`);
    hasErrors = true;
  } else {
    console.log(`    ✅ 请求超时: ${rules.request.timeout}ms`);
  }
  
  if (rules.request.retryCount < 1) {
    console.error(`    ❌ 重试次数过少: ${rules.request.retryCount}`);
    hasErrors = true;
  } else {
    console.log(`    ✅ 重试次数: ${rules.request.retryCount}`);
  }
  
  // 测试验证规则
  console.log('\n✅ 测试验证规则:');
  const validation = VALIDATION_RULES.dictionary;
  
  if (validation.title.maxLength < validation.title.minLength) {
    console.error(`    ❌ 标题最大长度小于最小长度`);
    hasErrors = true;
  } else {
    console.log(`    ✅ 标题长度规则: ${validation.title.minLength}-${validation.title.maxLength}`);
  }
  
  if (!validation.code.pattern || !(validation.code.pattern instanceof RegExp)) {
    console.error(`    ❌ 编码验证规则无效`);
    hasErrors = true;
  } else {
    console.log(`    ✅ 编码验证规则: ${validation.code.pattern}`);
  }
  
  // 总结
  console.log('\n' + '='.repeat(50));
  if (hasErrors) {
    console.error('❌ 配置测试失败，请修复上述错误');
    process.exit(1);
  } else {
    console.log('✅ 配置测试通过，所有配置项都正确');
  }
}

// 测试编码生成
function testCodeGeneration() {
  console.log('\n🔢 测试编码生成:');
  
  // 模拟编码生成函数
  function generateDictionaryCode(prefix = 'DISEASE', includeRandom = false) {
    const rules = GENERATION_RULES.codeGeneration;
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const time = new Date().toTimeString().slice(0, 8).replace(/:/g, '');
    
    let code = `${prefix}${rules.separator}${date}${time}`;
    
    if (includeRandom || rules.includeRandom) {
      const randomStr = Math.random().toString(36).substring(2, 2 + rules.randomLength).toUpperCase();
      code += `${rules.separator}${randomStr}`;
    }
    
    return code;
  }
  
  Object.entries(DICTIONARY_TYPES).forEach(([type, config]) => {
    const code = generateDictionaryCode(config.prefix);
    console.log(`  ${type}: ${code}`);
    
    // 验证生成的编码
    const validation = VALIDATION_RULES.dictionary.code;
    if (!validation.pattern.test(code)) {
      console.error(`    ❌ 生成的编码不符合验证规则`);
    } else {
      console.log(`    ✅ 编码格式正确`);
    }
  });
}

// 测试数据准备
function testDataPreparation() {
  console.log('\n📊 测试数据准备:');
  
  // 模拟数据准备函数
  function prepareDictionaryData(dataType = 'diseases') {
    const templateData = DICTIONARY_DATA_TEMPLATES[dataType] || DICTIONARY_DATA_TEMPLATES.diseases;
    
    return templateData.map(item => {
      const dictType = DICTIONARY_TYPES[item.type];
      return {
        title: item.title,
        value: item.value,
        dictionaryId: dictType.dictionaryId,
        sortOrder: item.sortOrder,
        type: item.type
      };
    });
  }
  
  Object.keys(DICTIONARY_DATA_TEMPLATES).forEach(dataType => {
    const data = prepareDictionaryData(dataType);
    console.log(`  ${dataType}: 准备了 ${data.length} 条数据`);
    
    // 检查数据完整性
    const hasIncompleteData = data.some(item => 
      !item.title || !item.value || !item.dictionaryId || typeof item.sortOrder !== 'number'
    );
    
    if (hasIncompleteData) {
      console.error(`    ❌ 存在不完整的数据`);
    } else {
      console.log(`    ✅ 所有数据完整`);
    }
  });
}

// 主测试函数
function runTests() {
  console.log('🚀 开始配置测试\n');
  
  try {
    testConfiguration();
    testCodeGeneration();
    testDataPreparation();
    
    console.log('\n🎉 所有测试通过！配置文件可以正常使用。');
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}

export { testConfiguration, testCodeGeneration, testDataPreparation, runTests };
