# 数据生成脚本快速参考

## 🚀 可用命令

### 字典值生成

```bash
# 生成疾病字典数据（默认）
pnpm run gen:data

# 测试模式生成疾病字典
pnpm run gen:data:test
```

### 机构字典管理

```bash
# 根据数据集动态管理机构字典状态
pnpm run gen:data:org

# 测试模式管理机构字典
pnpm run gen:data:org:test
```

### 帮助和配置

```bash
# 显示帮助信息
pnpm run gen:data:help

# 测试配置文件
pnpm run test:config
```

## 🔧 直接命令行调用

```bash
# 基本用法
node scripts/gendata/generate-production-data.js

# 生成疾病字典（测试模式）
node scripts/gendata/generate-production-data.js --type=diseases --test

# 管理机构字典（测试模式）
node scripts/gendata/generate-production-data.js --manage-organization --test

# 指定环境
node scripts/gendata/generate-production-data.js --env=staging

# 显示帮助
node scripts/gendata/generate-production-data.js --help
```

## 📊 功能说明

### 疾病字典生成
- **目标**: 生成疾病类型字典值
- **数据**: 药物成瘾、心理健康、焦虑障碍、睡眠障碍
- **字典ID**: 7
- **编码前缀**: DISEASE

### 机构字典管理
- **目标**: 根据数据集动态管理机构字典的启用/停用状态
- **逻辑**: 
  - 查询所有数据集
  - 提取机构信息（affiliatedUnit字段）
  - 在数据集中存在的机构 → 启用
  - 在数据集中不存在的机构 → 停用
- **字典ID**: 6
- **编码前缀**: ORGANIZATION

## ⚠️ 重要提醒

1. **先测试**: 建议先使用测试模式（`--test`）验证功能
2. **检查token**: 确保Authorization token有效
3. **网络连接**: 确保能访问目标服务器
4. **日志监控**: 查看 `./logs` 目录下的日志文件

## 🔍 故障排除

### 常见错误

1. **认证失败**: 检查Authorization token
2. **网络连接失败**: 检查服务器地址和网络
3. **数据格式错误**: 查看详细日志信息

### 调试模式

```bash
# 设置调试环境
NODE_ENV=development node scripts/gendata/generate-production-data.js --test
```

## 📁 相关文件

- `scripts/gendata/generate-production-data.js` - 主脚本
- `scripts/gendata/data-generation-config.js` - 配置文件
- `scripts/gendata/README.md` - 详细文档
- `scripts/gendata/example-usage.md` - 使用示例
- `logs/data-generation-*.log` - 执行日志

## 🎯 快速开始

1. **测试疾病字典生成**:
   ```bash
   pnpm run gen:data:test
   ```

2. **测试机构字典管理**:
   ```bash
   pnpm run gen:data:org:test
   ```

3. **实际执行**（确认测试无误后）:
   ```bash
   pnpm run gen:data        # 生成疾病字典
   pnpm run gen:data:org    # 管理机构字典
   ```

## 📈 执行结果示例

### 疾病字典生成结果
```
📚 diseases 字典值生成结果:
1. ✅ 药物成瘾 (DISEASE20250904163525)
2. ✅ 心理健康 (DISEASE20250904163527)
3. ✅ 焦虑障碍 (DISEASE20250904163529)
4. ✅ 睡眠障碍 (DISEASE20250904163530)
📈 总体成功率: 4/4 (100.0%)
```

### 机构字典管理结果
```
🏢 机构字典状态管理结果:
启用的机构: 1 个
1. ✅ 首都医科大学附属北京天坛医院 -> 启用
停用的机构: 1 个
1. ⏸️ 中国医学科学院 -> 停用
```
