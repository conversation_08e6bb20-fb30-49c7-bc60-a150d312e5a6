<template>
  <div class="flex h-full">
    <div class="aside bg-w flex flex-col">
      <div class="border-b border-[#e8eaed] py-2 pl-5">
        <el-checkbox v-model="unitAll" @change="onUnitChange">
          <span class="text-m text-lg font-bold">所有单位</span>
        </el-checkbox>
      </div>
      <el-scrollbar class="h-0 flex-1">
        <div class="pl-5">
          <el-checkbox-group :model-value="checkUnits" class="flex flex-col" @change="onUnitsChange">
            <el-checkbox v-for="item in units" :key="item.id" :value="item.id">
              {{ item.title }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-scrollbar>

      <div class="mt-4 border-b border-[#e8eaed] py-2 pl-5">
        <el-checkbox v-model="illnessAll" @change="onIllnessChange">
          <span class="text-m text-lg font-bold">所有疾病类型</span>
        </el-checkbox>
      </div>
      <el-scrollbar class="h-0 flex-1">
        <div class="pl-5">
          <el-checkbox-group :model-value="checkIllness" class="flex flex-col" @change="onIllnessesChange">
            <el-checkbox v-for="item in illnesses" :key="item.id" :value="item.id">
              <span>{{ item.title }}</span>
              <span>({{ item.amount || 0 }})</span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-scrollbar>
    </div>

    <div v-loading="tableLoading" class="w-0 flex-1">
      <el-scrollbar height="100%">
        <div class="px-10 pt-2">
          <div class="mt-2">
            <h3 class="mb-2 text-[28px]">数据统计</h3>
            <div class="grid h-[500px] grid-cols-2 gap-2">
              <Chart v-if="pieChartOptions" :option="pieChartOptions" />
              <div class="relative">
                <Chart :option="barCaseCount" />
                <div class="absolute top-0 right-[20px] rounded-lg bg-[#00BFFF] px-4 py-2 text-white shadow-lg">
                  <span class="text-sm font-medium">总病例数</span>
                  <span class="ml-2 text-xl font-bold">{{ totalCount.toLocaleString() }}</span>
                </div>
              </div>
              <!-- <Chart :option="pie" /> -->
            </div>
          </div>

          <div>
            <div class="mt-2 flex">
              <el-input
                v-model="fieldName"
                placeholder="请输入关键字搜索"
                style="width: 300px"
                clearable
                @clear="fetchTable()"
                @keyup.enter="fetchTable()"
              >
                <template #append>
                  <el-button :icon="Search" @click="fetchTable()" />
                </template>
              </el-input>
            </div>

            <el-table ref="tableRef" :data="tableData" style="width: 100%" class="c-table-header mt-2">
              <el-table-column prop="datasetNameCn" label="数据集名称(中文)" show-overflow-tooltip />
              <!-- <el-table-column prop="datasetName" label="数据集名称(英文)" show-overflow-tooltip /> -->
              <el-table-column prop="projectCode" label="课题编码缩写" />
              <!-- <el-table-column prop="description" label="数据集说明" show-overflow-tooltip /> -->
              <el-table-column prop="diseaseTypeAnnotation" label="疾病类型" />
              <el-table-column prop="createDate" label="更新日期" width="170px" />
              <el-table-column prop="caseCount" label="数量" width="120px"> </el-table-column>
              <el-table-column prop="projectLeader" label="项目负责人" width="120px" />
              <el-table-column prop="affiliatedUnit" label="所属单位" />
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button link type="primary" @click="onViewDetail(row)">查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-bottom">
              <el-pagination
                background
                layout="total, prev, pager, next, jumper"
                :page-size="pagination.pageSize"
                :total="total"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    findByBictionaryCode,
    findFileInforByAnnotationId,
    findTotalAmountByDictionaryCodeOrg,
    findTotalAmountByDictionaryCodeDisease,
    findPlatStatistic,
  } from '@/api/index';
  import { cloneDeep, map } from 'lodash-es';
  import { Search } from '@element-plus/icons-vue';
  import Chart from '@/components/Chart.vue';
  import { useRouter } from 'vue-router';
  import { download } from '@/utils/request';
  import { ElMessage } from 'element-plus';
  const router = useRouter();

  const loading = ref(false);
  const type = ref('');
  let ids: number[] = [];

  const unitAll = ref(true);
  const checkUnits = ref<number[]>([]);
  const units = ref<DictionaryValueStatisticDTO[]>([]);
  const onUnitChange = (value: number) => {
    if (value) {
      checkUnits.value = [];
      resetIllness();
    } else {
      unitAll.value = true;
    }
    type.value = '所有单位';
    ids = [];
  };
  const onUnitsChange = (value: number[]) => {
    if (value.length <= 0) {
      // 如果取消了所有选择，自动选中"所有单位"
      unitAll.value = true;
      checkUnits.value = [];
      type.value = '所有单位';
      ids = [];
      return;
    }
    unitAll.value = false;
    checkUnits.value = value;
    resetIllness();
    type.value = '单位' + value.join(',');
    ids = [];
    ids = value;
  };
  //重置单位值
  function resetUnit() {
    unitAll.value = false;
    checkUnits.value = [];
  }

  const illnessAll = ref(false);
  const checkIllness = ref<number[]>([]);
  const illnesses = ref<DictionaryValueStatisticDTO[]>([]);
  const onIllnessChange = (value: number) => {
    if (value) {
      checkIllness.value = [];
      resetUnit();
    } else {
      illnessAll.value = true;
    }
    type.value = '所有疾病';
    ids = [];
  };
  const onIllnessesChange = (value: number[]) => {
    if (value.length <= 0) {
      // 如果取消了所有选择，自动选中"所有疾病类型"
      illnessAll.value = true;
      checkIllness.value = [];
      type.value = '所有疾病';
      ids = [];
      return;
    }
    illnessAll.value = false;
    checkIllness.value = value;
    resetUnit();
    type.value = '疾病' + value.join(',');
    ids = [];
    ids = value;
  };
  //重置疾病类型的值
  function resetIllness() {
    illnessAll.value = false;
    checkIllness.value = [];
  }

  // 所有单位、所有疾病类型
  async function fetchTypes() {
    try {
      loading.value = true;
      const [unit] = await Promise.all([
        findByBictionaryCode(1, 999, { dictionaryCode: 'TYPE_ORGANIZATION' } as any),
        // findByBictionaryCode(1, 999, { dictionaryCode: 'TYPE_DISEASE' } as any),
      ]);
      units.value = unit.data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // 预设的20种美丽的颜色
  const chartColors = [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
    '#ea7ccc',
    '#ff9f7f',
    '#27a4f2',
    '#dd6236',
    '#7353ba',
    '#4cc09e',
    '#FF6600',
    '#c490d1',
    '#87c4a3',
    '#ffb980',
    '#2dd0d3',
    '#8378ea',
  ];

  // 用于生成不重复的颜色的方法
  const usedColors = new Set<string>();
  function generateUniqueColor(): string {
    // 生成饱和度和亮度适中的HSL颜色，不会太暗或太亮
    const h = Math.floor(Math.random() * 360);
    const s = Math.floor(Math.random() * 30 + 60); // 60-90%
    const l = Math.floor(Math.random() * 20 + 55); // 55-75%

    const color = `hsl(${h}, ${s}%, ${l}%)`;

    // 确保不重复
    if (usedColors.has(color)) {
      return generateUniqueColor();
    }

    usedColors.add(color);
    return color;
  }

  // 获取图表所需的所有颜色
  function getChartColors(dataLength: number): string[] {
    const colors: string[] = [...chartColors];

    // 如果需要更多颜色，则动态生成
    if (dataLength > chartColors.length) {
      for (let i = chartColors.length; i < dataLength; i++) {
        colors.push(generateUniqueColor());
      }
    }

    // 返回所需数量的颜色
    return colors.slice(0, dataLength);
  }

  async function fetchChartData() {
    const [res1, res2] = await Promise.all([
      findTotalAmountByDictionaryCodeOrg(),
      findTotalAmountByDictionaryCodeDisease(),
    ]);
    // setFileChart(res1.data!);
    // setIllnessChart(res2.data!);
    illnesses.value = res2.data || [];
  }

  //文件数量饼图
  function setFileChart(data: any[]) {
    const colors = getChartColors(data.length);
    let newOption = cloneDeep(option);
    newOption.title!.text = '单位数据统计';
    newOption.series.data = data.map((item) => {
      item.name = item.title;
      item.value = item.amount;
      return item;
    });
    pie.value = {
      ...newOption,
      color: colors,
    };
  }

  //疾病类型饼图
  function setIllnessChart(data: any[]) {
    const colors = getChartColors(data.length);
    let newOption = cloneDeep(option);
    newOption.title!.text = '疾病类型';
    newOption.series.data = data.map((item) => {
      item.name = item.title;
      item.value = item.amount;
      return item;
    });
    pie2.value = {
      ...newOption,
      color: colors,
    };
  }

  let option = {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'item',
    },
    series: {
      type: 'pie',
      radius: '50%',
      data: [] as { value: number; name: string }[],
      label: {
        color: '#939899',
        formatter: '{b} \n\r{@[]}({d}%)',
      },
    },
  };

  // 数据类型分布
  const pieChartOptions = ref({
    color: chartColors,
    title: {
      text: '数据类型分布',
      left: 'left',
      top: 0,
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
    },
    legend: {
      left: 'center',
      bottom: 0,
      icon: 'circle',
    },
    series: {
      name: '数据类型',
      type: 'pie',
      radius: ['20%', '40%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 4,
        borderWidth: 2,
        borderColor: '#fff',
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{c} ({d}%)',
        padding: [0, -40],
        alignTo: 'labelLine',
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 40,
        smooth: true,
      },
      data: [] as { value: number; name: string }[],
    },
  });

  // 获取数据类型分布和数据集病例数量
  async function fetchOverview() {
    try {
      const { data } = await findPlatStatistic();
      // 处理数据类型分布
      if (data?.dataTypeDistribution) {
        const pieData = map(data.dataTypeDistribution, (value, key) => ({
          value: value,
          name: key,
        }));

        // 更新数据和颜色
        const colors = getChartColors(pieData.length);
        pieChartOptions.value.series.data = pieData;
        pieChartOptions.value.color = colors;
      } else {
        pieChartOptions.value.series.data = [];
      }

      if (data?.caseCountInFileInfo) {
        createBarChart(data.caseCountInFileInfo as Record<string, number>);
      }
    } catch (error) {
      console.log(error);
    }
  }

  const totalCount = ref(0);
  // 数据集病例数量
  function createBarChart(caseCountMap: Record<string, number>) {
    // 存储原始数据集名称和缩短后的名称的映射关系
    const datasetNameMap = {};

    // 创建包含名称和值的数组，然后按值排序
    const dataEntries = Object.entries(caseCountMap).map(([name, value]) => ({
      originalName: name,
      shortName: '', // 初始化为空，后面会设置
      value: value,
    }));

    // 按值从大到小排序
    dataEntries.sort((a, b) => b.value - a.value);

    // 生成唯一的短名称
    const usedShortNames = new Set();
    dataEntries.forEach((entry) => {
      let shortName = entry.originalName.length > 10 ? entry.originalName.substring(0, 10) + '...' : entry.originalName;
      let counter = 1;

      // 如果短名称已存在，添加数字后缀确保唯一性
      while (usedShortNames.has(shortName)) {
        const baseName = entry.originalName.length > 10 ? entry.originalName.substring(0, 8) : entry.originalName;
        shortName = `${baseName}(${counter})...`;
        counter++;
      }

      usedShortNames.add(shortName);
      entry.shortName = shortName;
    });

    // 从排序后的数据中提取名称和值
    const sortedShortNames = dataEntries.map((entry) => {
      datasetNameMap[entry.shortName] = entry.originalName;
      return entry.shortName;
    });
    const sortedValues = dataEntries.map((entry) => entry.value);

    // 计算总和的函数
    totalCount.value = 0;
    for (const key in caseCountMap) {
      if (Object.prototype.hasOwnProperty.call(caseCountMap, key)) {
        totalCount.value += +caseCountMap[key];
      }
    }

    // 创建柱状图配置
    barCaseCount.value = {
      title: {
        text: '数据集病例数量',
      },
      color: ['#00BFFF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          // 在提示框中显示完整名称和数值
          const shortName = params[0].axisValue;
          const fullName = datasetNameMap[shortName] || shortName;
          const value = params[0].value;
          return `<div style="font-weight:bold">${fullName}</div>病例数量: ${value}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '20%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: sortedShortNames,
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 10,
          width: 60,
          overflow: 'truncate',
          align: 'right',
        },
      },
      yAxis: {
        type: 'value',
        name: '病例数量',
      },
      series: [
        {
          name: '病例数量',
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            borderRadius: [5, 5, 0, 0],
          },
          data: sortedValues,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
            fontSize: 12,
            distance: 5,
          },
        },
      ],
    };
  }

  const pie = ref<any>({});
  const pie2 = ref<any>({});
  const barCaseCount = ref<any>({});

  //表格
  const tableLoading = ref(false);
  const tableData = ref<FileInfoVO[]>([]);
  const fieldName = ref('');
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchTable(e);
  };
  async function fetchTable(pageNum = 1) {
    try {
      tableLoading.value = true;
      let params: AnnotationIDListDTO = {
        annotationIDList: ids,
        pageNum,
        pageSize: pagination.pageSize,
        otherFilter: fieldName.value,
      };
      const { data } = await findFileInforByAnnotationId(params);
      tableData.value = data?.content || [];
      total.value = data?.totalElement || 0;
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }

  //下载
  const onDownload = async (row) => {
    try {
      tableLoading.value = true;
      await download(`/FileInfor/getFileStream/${row.id}`, {
        method: 'get',
      });
      ElMessage({ type: 'success', message: '下载成功' });
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  };
  //查看详情
  const onViewDetail = (row) => {
    router.push({ name: 'DataSetField', params: { id: row.id } });
  };

  watch(type, (newObj, oldObj) => {
    if (newObj !== oldObj) {
      fetchTable();
    }
  });

  onMounted(async () => {
    type.value = '所有单位';
    await fetchOverview(); // 获取数据类型分布并创建病例数量柱状图
    fetchTypes();
    fetchChartData();
  });
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }

  .section {
    line-height: 1;
  }

  .el-menu {
    border-right: none;
  }
</style>
