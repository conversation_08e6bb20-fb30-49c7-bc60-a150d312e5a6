<template>
  <div class="flex h-full flex-col">
    <h2 class="bg-w flex h-[60px] items-center pl-[28px] text-xl font-bold">项目管理</h2>

    <div class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="px-10">
        <el-button type="primary" @click="onApply"> 申请项目 </el-button>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table v-loading="loading" :data="tableData" class="c-table-header" height="100%">
          <el-table-column label="ID" prop="applicationId" />
          <el-table-column label="项目名称" min-width="200px">
            <template #default="{ row }">
              <div class="flex items-center">
                <span>{{ row.applicationTitle }}</span>

                <el-popover
                  v-if="['审核未通过'].includes(row.applicationState)"
                  effect="light"
                  placement="bottom-start"
                  trigger="click"
                >
                  <template #reference>
                    <div class="audit-state" :style="`color: #E64545;`" @click="fetchOpinion(row)">
                      审核意见
                      <el-icon><CaretBottom /></el-icon>
                    </div>
                  </template>
                  <template #default>
                    <div v-loading="opinionLoading" class="text-xs">
                      <div class="text-tip">审核意见：</div>
                      {{ opinion }}
                    </div>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="申请时间">
            <template #default="{ row }">
              <span>{{ dayjs(row.applicationCreateTime).format('YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间">
            <template #default="{ row }">
              <EndTimeDisplay :create-time="row.applicationCreateTime" :duration="row.applicationDuration" />
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template #default="{ row }">
              <span class="status" :class="applicationStatusClass[row.applicationState]">
                {{ row.applicationState }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240">
            <template #default="{ row }">
              <!-- 如果有项目负责人角色，显示完整操作 -->
              <template v-if="hasApplicationAdministratorRole(row.roles)">
                <el-button link type="primary" @click="onView(row, 'true')"> 查看 </el-button>
                <el-button link type="primary" @click="onView(row)"> 编辑 </el-button>
                <el-popconfirm title="确定提交申请？" @confirm="onSubmit(row)">
                  <template #reference>
                    <el-button link type="primary"> 提交申请 </el-button>
                  </template>
                </el-popconfirm>
                <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary"> 删除 </el-button>
                  </template>
                </el-popconfirm>
              </template>
              <!-- 如果没有项目负责人角色，只显示查看 -->
              <template v-else>
                <el-button link type="primary" @click="onView(row, 'true')"> 查看 </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  /* 项目管理 */
  import EndTimeDisplay from '../../components/EndTimeDisplay.vue';
  import dayjs from 'dayjs';
  import { ElMessage } from 'element-plus';
  import {
    findApplicationUserByMdmUserId_1,
    deleteApplicationById,
    applyApplication,
    findVerifyResult_1,
  } from '@/api/index';
  import { CaretBottom } from '@element-plus/icons-vue';
  import { hasApplicationAdministratorRole } from '@/utils/util';
  import { useRouter } from 'vue-router';
  import { useUsers } from '@/store/user-info.js';
  import { applicationStatusClass } from '@/utils/constants';
  const router = useRouter();
  const store = useUsers();

  const tableData = ref<ApplicationUserVO[]>([]);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
    fetchData();
  };

  //查看编辑
  const onView = (row: ApplicationUserVO, readonly = 'false') => {
    router.push({ name: 'PersonalProjectEdit', query: { id: row.applicationId, readonly } });
  };

  //提交申请
  const onSubmit = async (row: ApplicationUserVO) => {
    try {
      loading.value = true;
      await applyApplication(row.applicationId!, store.user.id, {} as any);
      ElMessage({ type: 'success', message: '提交成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  //申请项目
  const onApply = () => {
    router.push({ name: 'PersonalProjectApplication' });
  };

  fetchData();
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findApplicationUserByMdmUserId_1({
        userId: store.user.id,
        pageNum,
        pageSize: pagination.pageSize,
      });
      total.value = data?.totalElements || 0;
      pagination.page = pageNum;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const onDel = async (row: ApplicationUserVO) => {
    try {
      loading.value = true;
      await deleteApplicationById([row.applicationId!]);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const opinion = ref('');
  const opinionLoading = ref(false);
  const fetchOpinion = async (row: ApplicationUserVO) => {
    try {
      opinionLoading.value = true;
      opinion.value = '';
      const { data } = await findVerifyResult_1(row.applicationId!);
      opinion.value = data?.description ?? '暂无';
    } catch (error) {
      console.log(error);
    } finally {
      opinionLoading.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  .status-text::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }

  .status-to-start::before {
    background: #e6a117;
  }

  .status-under-way::before {
    background: #24b383;
  }

  .status-finished::before {
    background: #939899;
  }

  .audit-state {
    text-align: center;
    border-width: 1px;
    border-style: solid;
    border-radius: 12px;
    margin-left: 12px;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    padding: 0 8px;
    cursor: pointer;
  }
</style>
